<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ChangeInformation</class>
 <widget class="QDialog" name="ChangeInformation">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>333</width>
    <height>410</height>
   </rect>
  </property>
  <property name="contextMenuPolicy">
   <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <property name="styleSheet">
   <string notr="true">border:none</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLabel" name="label">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="styleSheet">
        <string notr="true">font: 700 11pt &quot;Microsoft YaHei UI&quot;;</string>
       </property>
       <property name="text">
        <string>     编辑资料</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="but_cancelwindow">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>15</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>20</width>
         <height>20</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>20</width>
         <height>20</height>
        </size>
       </property>
       <property name="cursor">
        <cursorShape>PointingHandCursor</cursorShape>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
    border: none;
    background: transparent;
    color: rgb(78, 78, 78);
    font:  16pt 'Microsoft New Tai Lue';
}

QPushButton:hover {
    border: none;
    background: transparent;
    color: white;
    font:  16pt 'Microsoft New Tai Lue';
    background: red;
    border-radius: 3px;
}</string>
       </property>
       <property name="text">
        <string>x</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <widget class="QFrame" name="frame_message">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
       <horstretch>0</horstretch>
       <verstretch>210</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>210</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">border-radius: 10px;
border: none;
</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="1" column="0">
       <layout class="QHBoxLayout" name="horizontalLayout_6">
        <item>
         <widget class="QLabel" name="lab_account">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="styleSheet">
           <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border:none;
color:black;
</string>
          </property>
          <property name="text">
           <string>账号:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QTextEdit" name="textEdit_account">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>224</width>
            <height>38</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>224</width>
            <height>38</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
background: rgba(255, 255, 255, 0); 
border: none;
border-radius: 8px;
padding:5px;
</string>
          </property>
          <property name="verticalScrollBarPolicy">
           <enum>Qt::ScrollBarPolicy::ScrollBarAlwaysOff</enum>
          </property>
          <property name="horizontalScrollBarPolicy">
           <enum>Qt::ScrollBarPolicy::ScrollBarAlwaysOff</enum>
          </property>
          <property name="readOnly">
           <bool>true</bool>
          </property>
          <property name="html">
           <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
hr { height: 1px; border-width: 0; }
li.unchecked::marker { content: &quot;\2610&quot;; }
li.checked::marker { content: &quot;\2612&quot;; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Microsoft YaHei UI'; font-size:12pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;你的账号&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item row="4" column="0">
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <item>
         <widget class="QLabel" name="lab_sex">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="styleSheet">
           <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border:none;
color:black;
</string>
          </property>
          <property name="text">
           <string>性别:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="cbbox_sex">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>224</width>
            <height>38</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>224</width>
            <height>38</height>
           </size>
          </property>
          <property name="cursor">
           <cursorShape>PointingHandCursor</cursorShape>
          </property>
          <property name="whatsThis">
           <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;br/&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
          </property>
          <property name="styleSheet">
           <string notr="true">QComboBox {
    font: 12pt &quot;Microsoft YaHei UI&quot;;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    color: black;
    padding-right: 10px;
    padding-left: 3px;  
    text-align: left;  
    border-radius: 10px;
    background-color: white;
    padding:5px;
}
QComboBox::drop-down {
  border: none; 
}</string>
          </property>
          <item>
           <property name="text">
            <string>其他</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>男</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>女</string>
           </property>
          </item>
         </widget>
        </item>
       </layout>
      </item>
      <item row="2" column="0">
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <item>
         <widget class="QLabel" name="lab_nickname">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="styleSheet">
           <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border:none;
color:black;
</string>
          </property>
          <property name="text">
           <string>昵称:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="LineMessage" name="line_nickname">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Maximum">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>224</width>
            <height>38</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>224</width>
            <height>38</height>
           </size>
          </property>
          <property name="focusPolicy">
           <enum>Qt::FocusPolicy::ClickFocus</enum>
          </property>
          <property name="styleSheet">
           <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.1);
border-radius: 8px;
padding:5px;

</string>
          </property>
          <property name="text">
           <string>请输入昵称</string>
          </property>
          <property name="maxLength">
           <number>5</number>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item row="3" column="0">
       <layout class="QHBoxLayout" name="horizontalLayout_5">
        <item>
         <widget class="QLabel" name="lab_sig">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="styleSheet">
           <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border:none;
color:black;
</string>
          </property>
          <property name="text">
           <string>签名:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="LineMessage" name="line_sig">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Maximum">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>224</width>
            <height>38</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>224</width>
            <height>38</height>
           </size>
          </property>
          <property name="focusPolicy">
           <enum>Qt::FocusPolicy::ClickFocus</enum>
          </property>
          <property name="styleSheet">
           <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.1);
border-radius: 8px;
padding:5px;</string>
          </property>
          <property name="text">
           <string>请输入个性签名</string>
          </property>
          <property name="maxLength">
           <number>12</number>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item row="0" column="0">
       <layout class="QVBoxLayout" name="layout_frame">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <item>
           <widget class="QLabel" name="label_4">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>80</width>
              <height>80</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>47</width>
              <height>100</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border: none;;
color:black;
</string>
            </property>
            <property name="text">
             <string>头像:</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="LabelChooseAva" name="lab_avator">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>100</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>100</width>
              <height>100</height>
             </size>
            </property>
            <property name="cursor">
             <cursorShape>PointingHandCursor</cursorShape>
            </property>
            <property name="styleSheet">
             <string notr="true">border: 3px solid black;
 border-radius: 8px;</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="verticalSpacer">
          <property name="orientation">
           <enum>Qt::Orientation::Vertical</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Policy::Fixed</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>10</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item row="5" column="0">
       <layout class="QHBoxLayout" name="horizontalLayout_7">
        <item>
         <widget class="QPushButton" name="but_yes">
          <property name="enabled">
           <bool>false</bool>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>35</height>
           </size>
          </property>
          <property name="cursor">
           <cursorShape>PointingHandCursor</cursorShape>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
    font: 12pt &quot;Microsoft YaHei UI&quot;;
    background-color: rgb(167, 214, 255); 
    color: white;
    border-radius: 10px;
}
</string>
          </property>
          <property name="text">
           <string>确认</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="but_no">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>35</height>
           </size>
          </property>
          <property name="cursor">
           <cursorShape>PointingHandCursor</cursorShape>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
    font: 12pt 'Microsoft YaHei UI';
    background-color: rgb(240, 240, 240);
    color: black; 
    border: 0.5px solid rgb(220, 220, 220);
    border-radius: 10px;
}
QPushButton:hover {
    background-color: rgba(210, 210, 210, 0.7);
    color: black;
    border-radius: 10px;
}
QPushButton:pressed {
    background-color: rgba(200, 200, 200, 0.8);
    color: rgba(0, 0, 0, 0.9);
    border-radius: 10px;
}</string>
          </property>
          <property name="text">
           <string>取消</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>LineMessage</class>
   <extends>QLineEdit</extends>
   <header location="global">changeinformation.h</header>
  </customwidget>
  <customwidget>
   <class>LabelChooseAva</class>
   <extends>QLabel</extends>
   <header location="global">changeinformation.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
