<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RegisterWindow</class>
 <widget class="QMainWindow" name="RegisterWindow">
  <property name="windowModality">
   <enum>Qt::WindowModality::WindowModal</enum>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>350</width>
    <height>510</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>350</width>
    <height>510</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>350</width>
    <height>510</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>注册账号</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout_2">
    <item row="0" column="0">
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <widget class="QLabel" name="label_4">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>80</width>
          <height>80</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>47</width>
          <height>100</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;

color:black;
</string>
        </property>
        <property name="text">
         <string>头像:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="LabelRegis" name="lab_avator">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>100</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>100</width>
          <height>100</height>
         </size>
        </property>
        <property name="cursor">
         <cursorShape>PointingHandCursor</cursorShape>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item row="1" column="0">
     <layout class="QGridLayout" name="gridLayout">
      <item row="1" column="1" alignment="Qt::AlignmentFlag::AlignLeft">
       <widget class="QComboBox" name="cbbox_sex">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>224</width>
          <height>38</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>224</width>
          <height>38</height>
         </size>
        </property>
        <property name="cursor">
         <cursorShape>PointingHandCursor</cursorShape>
        </property>
        <property name="contextMenuPolicy">
         <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
        </property>
        <property name="whatsThis">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;br/&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="styleSheet">
         <string notr="true">QComboBox {
    font: 12pt &quot;Microsoft YaHei UI&quot;;
    border: 1px solid rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    color: black;
    padding-right: 10px;
    padding-left: 3px;  
    text-align: left;  
    border-radius: 10px;
    background-color: white;
}
QComboBox::drop-down {
  border: none; 
}</string>
        </property>
        <item>
         <property name="text">
          <string>其他</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>男</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>女</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="5" column="0">
       <widget class="QLabel" name="lab_question">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="styleSheet">
         <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;

color:black;
</string>
        </property>
        <property name="text">
         <string>问题答案:</string>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="LineRegis" name="line_question">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>224</width>
          <height>38</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>224</width>
          <height>38</height>
         </size>
        </property>
        <property name="contextMenuPolicy">
         <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3);
border-radius: 8px;
color:grey;
</string>
        </property>
        <property name="text">
         <string>请输入问题</string>
        </property>
        <property name="maxLength">
         <number>12</number>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="lab_sex">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="styleSheet">
         <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;

color:black;
</string>
        </property>
        <property name="text">
         <string>性      别:</string>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_password2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="styleSheet">
         <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;

color:black;
</string>
        </property>
        <property name="text">
         <string>确认密码:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="LineRegis" name="line_password">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>224</width>
          <height>38</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>224</width>
          <height>38</height>
         </size>
        </property>
        <property name="contextMenuPolicy">
         <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3);
border-radius: 8px;
color:grey;
</string>
        </property>
        <property name="text">
         <string>请输入密码</string>
        </property>
        <property name="maxLength">
         <number>12</number>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="LineRegis" name="line_password2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>224</width>
          <height>38</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>224</width>
          <height>38</height>
         </size>
        </property>
        <property name="contextMenuPolicy">
         <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3);
border-radius: 8px;
color:grey;
</string>
        </property>
        <property name="text">
         <string>请确认密码</string>
        </property>
        <property name="maxLength">
         <number>12</number>
        </property>
       </widget>
      </item>
      <item row="5" column="1">
       <widget class="LineRegis" name="line_answer">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>224</width>
          <height>38</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>224</width>
          <height>38</height>
         </size>
        </property>
        <property name="contextMenuPolicy">
         <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3);
border-radius: 8px;
color:grey;
</string>
        </property>
        <property name="text">
         <string>请输入答案</string>
        </property>
        <property name="maxLength">
         <number>12</number>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="lab_password">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="styleSheet">
         <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;

color:black;
</string>
        </property>
        <property name="text">
         <string>你的密码:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="LineRegis" name="line_nickname">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Maximum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>224</width>
          <height>38</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>224</width>
          <height>38</height>
         </size>
        </property>
        <property name="contextMenuPolicy">
         <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3);
border-radius: 8px;
color:grey;
</string>
        </property>
        <property name="text">
         <string>请输入昵称</string>
        </property>
        <property name="maxLength">
         <number>5</number>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="lab_nickname">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="styleSheet">
         <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;

color:black;
</string>
        </property>
        <property name="text">
         <string>你的昵称:</string>
        </property>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="label_question">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="styleSheet">
         <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;

color:black;
</string>
        </property>
        <property name="text">
         <string>密保问题:</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item row="2" column="0">
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Policy::Maximum</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="submit_but">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>15</horstretch>
          <verstretch>15</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>170</width>
          <height>60</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>5</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="cursor">
         <cursorShape>ArrowCursor</cursorShape>
        </property>
        <property name="contextMenuPolicy">
         <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    font: 14pt &quot;Microsoft YaHei UI&quot;;
    background-color: rgb(167, 214, 255); 
    color: white;; 
   border-radius: 15px;
}
</string>
        </property>
        <property name="text">
         <string>提交</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Policy::Maximum</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>350</width>
     <height>18</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <customwidgets>
  <customwidget>
   <class>LineRegis</class>
   <extends>QLineEdit</extends>
   <header location="global">registerwindow.h</header>
  </customwidget>
  <customwidget>
   <class>LabelRegis</class>
   <extends>QLabel</extends>
   <header location="global">registerwindow.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
