<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Login</class>
 <widget class="QMainWindow" name="Login">
  <property name="windowModality">
   <enum>Qt::WindowModality::NonModal</enum>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>500</width>
    <height>320</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>500</width>
    <height>320</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>500</width>
    <height>320</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>速聊-少数人的社交软件</string>
  </property>
  <property name="windowIcon">
   <iconset theme="QIcon::ThemeIcon::CameraWeb"/>
  </property>
  <property name="windowOpacity">
   <double>0.995000000000000</double>
  </property>
  <property name="styleSheet">
   <string notr="true"> border: none;

</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0" colspan="2">
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <item alignment="Qt::AlignmentFlag::AlignLeft">
       <widget class="QLabel" name="lab_tubiao">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>30</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>30</width>
          <height>30</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item alignment="Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter">
       <widget class="QLabel" name="label">
        <property name="styleSheet">
         <string notr="true">qproperty-alignment: 'AlignLeft';
font: 10pt ;
</string>
        </property>
        <property name="text">
         <string>速聊</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_5">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Policy::Fixed</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>330</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </item>
    <item row="0" column="2" alignment="Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTop">
     <widget class="QPushButton" name="but_deletewindow">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
        <horstretch>0</horstretch>
        <verstretch>15</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>20</width>
        <height>20</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>20</width>
        <height>20</height>
       </size>
      </property>
      <property name="cursor">
       <cursorShape>PointingHandCursor</cursorShape>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton {
    border: none;
    background: transparent;
    color: rgb(78, 78, 78);
    font:  16pt 'Microsoft New Tai Lue';
}

QPushButton:hover {
    border: none;
    background: transparent;
    color: white;
    font:  16pt 'Microsoft New Tai Lue';
    background: red;
    border-radius: 3px;
}</string>
      </property>
      <property name="text">
       <string>x</string>
      </property>
     </widget>
    </item>
    <item row="1" column="1">
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <spacer name="horizontalSpacer_3">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item alignment="Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignVCenter">
       <widget class="QLabel" name="lab_avator">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>100</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>100</width>
          <height>100</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">border: 3px solid black;
border-radius: 8px;</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_4">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </item>
    <item row="2" column="0">
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Policy::Expanding</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>92</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item alignment="Qt::AlignmentFlag::AlignBottom">
       <widget class="QPushButton" name="rgs_pbt">
        <property name="styleSheet">
         <string notr="true">QPushButton {
       font: 10pt &quot;Microsoft YaHei UI&quot;;
       border: none;
       text-decoration: none; 
       color: gray;
   }

   QPushButton:hover {
       text-decoration: underline; 
       color: gray;
   }</string>
        </property>
        <property name="text">
         <string>注册账号</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item row="2" column="1">
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <widget class="LineInput" name="line_qqnum">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>289</width>
          <height>37</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>289</width>
          <height>37</height>
         </size>
        </property>
        <property name="cursor">
         <cursorShape>IBeamCursor</cursorShape>
        </property>
        <property name="tabletTracking">
         <bool>false</bool>
        </property>
        <property name="contextMenuPolicy">
         <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
        </property>
        <property name="acceptDrops">
         <bool>true</bool>
        </property>
        <property name="autoFillBackground">
         <bool>false</bool>
        </property>
        <property name="styleSheet">
         <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3);
border-radius: 10px;
color:grey;
</string>
        </property>
        <property name="inputMethodHints">
         <set>Qt::InputMethodHint::ImhNone</set>
        </property>
        <property name="inputMask">
         <string/>
        </property>
        <property name="text">
         <string>请输入你的账号</string>
        </property>
        <property name="maxLength">
         <number>12</number>
        </property>
        <property name="frame">
         <bool>true</bool>
        </property>
        <property name="echoMode">
         <enum>QLineEdit::EchoMode::Normal</enum>
        </property>
        <property name="cursorPosition">
         <number>7</number>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignCenter</set>
        </property>
        <property name="placeholderText">
         <string/>
        </property>
        <property name="cursorMoveStyle">
         <enum>Qt::CursorMoveStyle::VisualMoveStyle</enum>
        </property>
        <property name="clearButtonEnabled">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="LineInput" name="line_password">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>289</width>
          <height>37</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>289</width>
          <height>37</height>
         </size>
        </property>
        <property name="contextMenuPolicy">
         <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
        </property>
        <property name="acceptDrops">
         <bool>true</bool>
        </property>
        <property name="styleSheet">
         <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3);
border-radius: 10px;
color:grey;
</string>
        </property>
        <property name="text">
         <string>请输入你的密码</string>
        </property>
        <property name="maxLength">
         <number>12</number>
        </property>
        <property name="frame">
         <bool>true</bool>
        </property>
        <property name="echoMode">
         <enum>QLineEdit::EchoMode::Normal</enum>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignCenter</set>
        </property>
        <property name="placeholderText">
         <string/>
        </property>
        <property name="cursorMoveStyle">
         <enum>Qt::CursorMoveStyle::VisualMoveStyle</enum>
        </property>
        <property name="clearButtonEnabled">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <widget class="QCheckBox" name="ckbox_remeber">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Maximum">
            <horstretch>50</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="styleSheet">
           <string notr="true"> color: gray;</string>
          </property>
          <property name="text">
           <string>记住密码</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="auto_ckbox">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Maximum">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="styleSheet">
           <string notr="true"> color: gray;</string>
          </property>
          <property name="text">
           <string>自动登录</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="fgt_pbt">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Maximum">
            <horstretch>50</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="styleSheet">
           <string notr="true"> QPushButton {
       font: 10pt &quot;Microsoft YaHei UI&quot;;
       border: none;
       text-decoration: none; 
      color: gray;
   }

   QPushButton:hover {
       text-decoration: underline; 
       color: gray;
   }</string>
          </property>
          <property name="text">
           <string>忘记密码</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QPushButton" name="gologin">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>28</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>289</width>
          <height>37</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>289</width>
          <height>37</height>
         </size>
        </property>
        <property name="cursor">
         <cursorShape>PointingHandCursor</cursorShape>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    font: 12pt &quot;Microsoft YaHei UI&quot;;
    background-color: rgb(167, 214, 255); 
    color: white;; 
    border-radius: 15px;
}
</string>
        </property>
        <property name="text">
         <string>安全登录</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item row="2" column="2">
     <spacer name="horizontalSpacer_2">
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>91</width>
        <height>20</height>
       </size>
      </property>
     </spacer>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>500</width>
     <height>18</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuQQ">
    <property name="title">
     <string/>
    </property>
   </widget>
   <addaction name="menuQQ"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <customwidgets>
  <customwidget>
   <class>LineInput</class>
   <extends>QLineEdit</extends>
   <header location="global">login.h</header>
   <slots>
    <slot>mousePressEvent()</slot>
   </slots>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
